import React from "react";
import { renderToStaticMarkup } from "react-dom/server";
import { Jss<PERSON><PERSON><PERSON>, SheetsReg<PERSON>ry, createGenerateId } from "react-jss";
import { JSDOM } from "jsdom";
import { Previewer } from "pagedjs";
import Body from "../components/body";
import { getPropertiesString } from "../components/utils/setLayoutProperties";

module.exports = ({ letter, layout, extra }) => {

  const { window } = new JSDOM("<!DOCTYPE html><html><body></body></html>");
  global.window = window;
  global.document = window.document;

  const body = renderToStaticMarkup(
    <JssProvider registry={sheets} generateId={generateId}>
      <Body letter={letter} layout={layout} extra={extra} />
    </JssProvider>
  );

  return `
    <html>
      <head>
        <style>
        body{
          font-family: ${layout.paper?.fontFamily || "vazir"};
          font-size: ${layout.paper?.fontSize || "14"}px;
        }
          :root{
            ${getPropertiesString(layout)}
          }
        </style>
        <meta charset="utf-8">
        <script src="https://unpkg.com/pagedjs/dist/paged.polyfill.js"></script>
        <style>
          .all-header {
            position: running(AH);
          }

          .all-footer {
            position: running(AF);
          }

          .counter-fa::before {
            content: counter(page) " از " counter(pages);
          }

          .counter-en{
            direction: ltr;
          }

          .counter-en::before {
            content: counter(page) " of " counter(pages);
          }

          @page {
            size: ${layout.paper.width}cm ${layout.paper.height}cm;
             background: var(--page-background) no-repeat center center;
              background-size: contain;
            @top-center {
              content: element(AH);
            }
            @bottom-center {
              content: element(AF);
            }
          }
        </style>
        <style>
          html {
            -webkit-print-color-adjust: 'exact';
            -webkit-text-size-adjust: none;
          }
          table, figure {
            margin: 0;
            padding: 0;
            border: 0;
            text-size-adjust: none;
            -webkit-text-size-adjust: none;
            font-size: inherit;
          }
          table, figure {
            margin: 0;
            padding: 0;
            border: 0;
          }
          .alignment-DraftStyleDefault-alignLeft {
            text-align: left;
          }
          .alignment-DraftStyleDefault-alignCenter {
            text-align: center;
          }
          .alignment-DraftStyleDefault-alignRight {
            text-align: right;
          }
          .alignment-DraftStyleDefault-alignJustify {
            text-align: justify;
          }
          .alignment-DraftStyleDefault-rtl {
            direction: rtl;
          }
          .alignment-DraftStyleDefault-ltr {
            direction: ltr;
          }
        </style>
        <style>${sheets.toString()}</style>
      </head>
      ${body}
    </html>
  `;
};
