<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- <title>Vite + React + TS</title> -->
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
      }
      html {
        -webkit-print-color-adjust: "exact";
        -webkit-text-size-adjust: none;
      }
      table,
      figure {
        margin: 0;
        padding: 0;
        border: 0;
        text-size-adjust: none;
        -webkit-text-size-adjust: none;
        font-size: inherit;
      }
      table,
      figure {
        margin: 0;
        padding: 0;
        border: 0;
      }
      .alignment-DraftStyleDefault-alignLeft {
        text-align: left;
      }
      .alignment-DraftStyleDefault-alignCenter {
        text-align: center;
      }
      .alignment-DraftStyleDefault-alignRight {
        text-align: right;
      }
      .alignment-DraftStyleDefault-alignJustify {
        text-align: justify;
      }
      .alignment-DraftStyleDefault-rtl {
        direction: rtl;
      }
      .alignment-DraftStyleDefault-ltr {
        direction: ltr;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/print/index.jsx"></script>
  </body>
</html>
