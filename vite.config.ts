import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: [
      { find: "@", replacement: path.resolve(__dirname, "./src") },
      ...["body"].map((dir) => ({
        find: dir,
        replacement: path.resolve(process.cwd(), `${dir}`),
      })),
    ],
  },
  build: {
    outDir: "dist/preview",
    lib: {
      entry: path.resolve(__dirname, "src/index.jsx"),
      name: "Preview",
      fileName: (format) => `Preview.${format}.js`,
      formats: ["es", "cjs", "umd"],
    },
    rollupOptions: {
      // Exclude React from the final bundle (use peerDependencies in package.json)
      external: ["react", "react-dom"],
      // output: {
      //   globals: {
      //     react: "React",
      //     "react-dom": "ReactDOM",
      //   },
      // },
    },
  },
});
