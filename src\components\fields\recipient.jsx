import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeTokenArray,
  tokenLinesHandler
} from "./common";
import config from "./config";
import { lineFormatter, nameFormatter } from "./patterns";

const useStyles = createUseStyles({
  customRtlField,
  recipient: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.recipient),
    "& span": {
      fontSize: `${
        props.recipient.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.recipient.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.recipient),
  tokenNodes: (props) => commonTokenNodes(props.recipient),
});

const Recipient = (props) => {
  const classes = useStyles({ recipient: props.recipient });
  const label = localizeLabel(props.recipient);

  localizeTokenArray(props.recipient, props?.letter?.recipient);

  const recipientArr = props?.recipient?.lines
    ? lineFormatter(
      props?.letter?.recipient,
      props?.recipient?.lines,
      props?.recipient?.lang,
    )
    // backward
    : nameFormatter(
      props?.letter?.recipient,
      props?.recipient?.isSingleLine,
      props?.recipient?.isTitleFullNameFirst,
      props?.recipient?.isFullNameOnly,
      props?.recipient?.lang,
    );

  return (
    <div className={classes.bodyHeight}>
      <div className={classes.recipient} id={"recipientToken"}>
        {
          label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>
          {recipientArr.map((recipient, i) =>
            Array.isArray(recipient) ? (
              props?.recipient?.lines
                ? tokenLinesHandler(recipient)
                : recipient.map((r, i) => <div key={i}>{r}</div>) // backward
            ) : (
              props?.recipient?.lines
                ? tokenLinesHandler(recipient)
                : <div key={i}>{recipient}</div> // backward
            )
          )}
        </span>
      </div>
    </div>
  );
};

Recipient.propTypes = {
  recipient: PropTypes.object,
  letter: PropTypes.object.isRequired,
};
export default Recipient;
