import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import { lineFormatter, nameFormatter } from "./patterns";
import { convertDigitsToPersianDeepArray } from "../utils/convertDigitsToPersian";
import config from "./config";
import { tokenLinesHandler } from "./common";

const useStyles = createUseStyles({
  signerName: (props) => ({
    direction: "rtl",
    whiteSpace: "pre",
    "& span": {
      fontSize: `${
        props.signature.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.signature.fontWeight || "normal",
      width: "100%",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  }
});

const SignerName = (props) => {
  const { signature, letter, signInfo } = props;
  const classes = useStyles({ signature });

  convertDigitsToPersianDeepArray(props.letter.signerName);

  const signerNameArr = signature?.lines
    ? lineFormatter(
      [signInfo],
      signature?.lines,
      signature?.lang,
    )
    // backward
    : nameFormatter(
      [signInfo],
      signature?.isSingleLine,
      signature?.isTitleFullNameFirst,
      signature?.isFullNameOnly,
      signature?.lang,
    );

  return (
    <div className={classes.bodyHeight}>
      <div className={classes.signerName} id={'signerNameToken'}>
      <span>
        {signerNameArr.map((signerName, i) =>
          Array.isArray(signerName) ? (
            signature?.lines
              ? tokenLinesHandler(signerName)
              : signerName.map((r, i) => <div key={i}>{r}</div>) // backward
          ) : (
            signature?.lines
              ? tokenLinesHandler(signerName)
              : <div key={i}>{signerName}</div> // backward
          )
        )}
      </span>
      </div>
    </div>
  );
};

SignerName.propTypes = {
  signature: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default SignerName;
