import React from 'react';
import Recipient from '../fields/recipient';
import Attachment from '../fields/attachment';
import Cc from '../fields/cc';
import Confidentiality from '../fields/confidentiality';
import Logo from '../fields/logo';
import Priority from '../fields/priority';
import Sender from '../fields/sender';
import Signature from '../fields/signature';
import Subject from '../fields/subject';
import LetterNumber from '../fields/letterNumber';
import PageNumber from '../fields/pageNumber';
import StaticText from '../fields/staticText';
import Date from '../fields/date';
import Stamp from '../fields/stamp';

const components = (token, children, letter, property, options, key) => {

  let staticTextChildren;
  if (token.startsWith('staticText#')) {
    staticTextChildren = children[token];
    token = token.split('#')[0];
  }
  const tokens = {
    date: <Date date={children.date} letter={letter} key={key} />,
    recipient: <Recipient recipient={children.recipient} letter={letter} key={key} />,
    attachment: <Attachment attachment={children.attachment} letter={letter} key={key} />,
    cc: <Cc cc={children.cc} letter={letter} key={key} />,
    letterNumber: <LetterNumber letterNumber={children.letterNumber} letter={letter} key={key} fontFamily={property.fontFamily} />,
    confidentiality: <Confidentiality confidentiality={children.confidentiality} letter={letter} key={key} />,
    logo: <Logo logo={children.logo} letter={letter} key={key} />,
    stamp:<Stamp stamp={children.stamp} letter={letter} key={key} />,
    priority: <Priority priority={children.priority} letter={letter} key={key} />,
    sender: <Sender sender={children.sender} letter={letter} key={key} />,
    signature: <Signature signature={children.signature} letter={letter} property={property} options={options} key={key} />,
    subject: <Subject subject={children.subject} letter={letter} key={key} />,
    pageNumber: <PageNumber pageNumber={children.pageNumber} letter={letter} key={key} />,
    staticText: <StaticText staticText={staticTextChildren} key={key}/>
  };
  return tokens[token];
};

export default components;

