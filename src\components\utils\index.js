export const p2c = (valuePixel) => (valuePixel * 2.54) / 96;

export const c2p = (valueCM) => (valueCM * 96) / 2.54;

function isRTL(sandbox) {
  const rtlChars = "\u0591-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC";
  const rtlDirCheck = new RegExp(`^[^${rtlChars}]*?[${rtlChars}]`);

  return rtlDirCheck.test(sandbox);
}

export const wrapDir = (str) =>
  isRTL(str) ? `\u202A${str}\u202C` : `\u200E${str}\u200E`;

export const formatLetterNumber = (data) => {
  if (data) {
    return data
      .replaceAll("\u200E", "")
      .replaceAll("\u202A", "")
      .replaceAll("\u202C", "")
      ?.split("")
      ?.map((v) => wrapDir(v))
      ?.join("");
  }
  return data;
};

export const c2pInObject = (obj, excludes) => {
  Object.keys(obj).forEach(function (key) {
    if (typeof obj[key] === "object" && obj[key] !== null) {
      return c2pInObject(obj[key], excludes);
    }
    if (typeof obj[key] === "number") {
      if (!excludes || !excludes.includes(key)) {
        obj[key] = +c2p(obj[key]).toFixed(1);
      }
    }
  });
  return obj;
};

export const hasShowableChildren = (children, letter) => {
  const tokens = { ...children };
  if (!tokens) {
    return false;
  }
  if (tokens.signature) {
    if (
      letter.signedBy &&
      Array.isArray(letter.signedBy) &&
      letter.signedBy.length === 0
    ) {
      delete tokens.signature;
    }
  }
  return Object.keys(tokens).length > 0;
};

export const CreateCssBlob = () => {
  const customCss = `
    .all-header {
      position: running(AH);
    }

    .all-footer {
      position: running(AF);
    }

    @page {
      // size: A4;
      background: var(--page-background) no-repeat center center;
      background-size: cover;

      @top-center {
        content: element(AH);
      }
      @bottom-center {
        content: element(AF);
      }
    }
  `;

  const cssBlob = new Blob([customCss], {
    type: "text/css",
  });

  return URL.createObjectURL(cssBlob);
};
