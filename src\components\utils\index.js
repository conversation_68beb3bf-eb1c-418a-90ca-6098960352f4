import fonts from "../fonts";
import fonts_wd from "../fonts_wd";
import editorCSS from "com-chargoon-cloud-front-editor-2/lib/main.css?inline";

export const p2c = (valuePixel) => (valuePixel * 2.54) / 96;

export const c2p = (valueCM) => (valueCM * 96) / 2.54;

function isRTL(sandbox) {
  const rtlChars = "\u0591-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC";
  const rtlDirCheck = new RegExp(`^[^${rtlChars}]*?[${rtlChars}]`);

  return rtlDirCheck.test(sandbox);
}

export const wrapDir = (str) =>
  isRTL(str) ? `\u202A${str}\u202C` : `\u200E${str}\u200E`;

export const formatLetterNumber = (data) => {
  if (data) {
    return data
      .replaceAll("\u200E", "")
      .replaceAll("\u202A", "")
      .replaceAll("\u202C", "")
      ?.split("")
      ?.map((v) => wrapDir(v))
      ?.join("");
  }
  return data;
};

export const c2pInObject = (obj, excludes) => {
  Object.keys(obj).forEach(function (key) {
    if (typeof obj[key] === "object" && obj[key] !== null) {
      return c2pInObject(obj[key], excludes);
    }
    if (typeof obj[key] === "number") {
      if (!excludes || !excludes.includes(key)) {
        obj[key] = +c2p(obj[key]).toFixed(1);
      }
    }
  });
  return obj;
};

export const hasShowableChildren = (children, letter) => {
  const tokens = { ...children };
  if (!tokens) {
    return false;
  }
  if (tokens.signature) {
    if (
      letter.signedBy &&
      Array.isArray(letter.signedBy) &&
      letter.signedBy.length === 0
    ) {
      delete tokens.signature;
    }
  }
  return Object.keys(tokens).length > 0;
};

export const CreateCssBlob = () => {
  // Create font CSS
  const fontFaces = [
    { name: "vazir", data: fonts.vazir },
    { name: "vazir_wd", data: fonts_wd.vazir },
    { name: "lotus", data: fonts.lotus },
    { name: "mitra", data: fonts.mitra },
    { name: "yekan", data: fonts.yekan },
    { name: "nazanin", data: fonts.nazanin },
    { name: "yaghot", data: fonts.yaghot },
    { name: "yaghotBold", data: fonts.yaghotBold },
    { name: "b-titr", data: fonts["b-titr"] },
    { name: "b-traffic", data: fonts["b-traffic"] },
    { name: "b-zar", data: fonts["b-zar"] },
    { name: "sahel", data: fonts.sahel },
    { name: "myriad-pro", data: fonts["myriad-pro"] },
    { name: "iran", data: fonts.iran },
  ];

  const fontCSS = fontFaces
    .filter((font) => font.data) // Only include fonts that exist
    .map(
      (font) => `
      @font-face {
        font-family: "${font.name}";
        src: url(data:font/otf;base64,${font.data});
      }
    `
    )
    .join("\n");

  // Sanitize editor CSS to remove problematic URLs and content
  const sanitizeCSS = (css) => {
    return (
      css
        // Remove url() references that might be invalid
        .replace(/url\([^)]*\)/g, "")
    );
    // // Remove @import statements
    // .replace(/@import[^;]*;/g, '')
    // // Remove any remaining problematic URL patterns
    // .replace(/background-image:\s*url\([^)]*\);?/g, '')
    // .replace(/background:\s*url\([^)]*\)[^;]*;?/g, '')
    // // Remove content properties that might contain problematic values
    // .replace(/content:\s*[^;]*;/g, '')
    // // Remove any CSS that might reference external resources
    // .replace(/@font-face[^}]*}/g, '')
    // // Remove any CSS variables that might be undefined
    // .replace(/var\([^)]*\)/g, '""')
    // // Remove any calc() expressions that might be problematic
    // .replace(/calc\([^)]*\)/g, '0');
  };

  const sanitizedEditorCSS = sanitizeCSS(editorCSS);

  const customCss = `
    ${fontCSS}

    .all-header {
      position: running(AH);
    }

    .all-footer {
      position: running(AF);
    }

    @page {
      // size: A4;
      background: var(--page-background) no-repeat center center;
      background-size: cover;

      @top-center {
        content: element(AH);
      }
      @bottom-center {
        content: element(AF);
      }
    }
  `;

  const cssBlob = new Blob([customCss], {
    type: "text/css",
  });

  return URL.createObjectURL(cssBlob);
};
