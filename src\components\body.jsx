import React from "react";

import { createUseStyles } from "react-jss";
// import { JSDOM } from "jsdom";

import WithBody from "./withBody";
import components from "./utils/components";
import Editor from "com-chargoon-cloud-front-editor";
import { StaticEditor } from "com-chargoon-cloud-front-editor-2";
import "com-chargoon-cloud-front-editor/lib/index.css";
import fonts from "./fonts";
import config from "./fields/config";
import fonts_wd from "./fonts_wd";

const hasShowableChildren = (children, letter) => {
  const tokens = { ...children };
  if (!tokens) {
    return false;
  }
  if (tokens.signature) {
    if (letter.signedBy && Array.isArray(letter.signedBy) && letter.signedBy.length === 0) {
      delete tokens.signature;
    }
  }
  return Object.keys(tokens).length > 0;
};

const useStyles = createUseStyles({
  "@global": {
    "@font-face": [
      {
        fontFamily: "vazir",
        src: `url(data:font/otf;base64,${fonts.vazir})`,
      },
      {
        fontFamily: "vazir_wd",
        src: `url(data:font/otf;base64,${fonts_wd.vazir})`,
      },
      {
        fontFamily: "lotus",
        src: `url(data:font/otf;base64,${fonts.lotus})`,
      },
      {
        fontFamily: "mitra",
        src: `url(data:font/otf;base64,${fonts.mitra})`,
      },
      {
        fontFamily: "yekan",
        src: `url(data:font/otf;base64,${fonts.yekan})`,
      },
      {
        fontFamily: "nazanin",
        src: `url(data:font/otf;base64,${fonts.nazanin})`,
      },
      {
        fontFamily: "yaghot",
        src: `url(data:font/otf;base64,${fonts.yaghot})`,
      },
      {
        fontFamily: "yaghotBold",
        src: `url(data:font/otf;base64,${fonts.yaghotBold})`,
      },
      {
        fontFamily: "b-titr",
        src: `url(data:font/otf;base64,${fonts["b-titr"]})`,
      },
      {
        fontFamily: "b-traffic",
        src: `url(data:font/otf;base64,${fonts["b-traffic"]})`,
      },
      {
        fontFamily: "b-zar",
        src: `url(data:font/otf;base64,${fonts["b-zar"]})`,
      },
      {
        fontFamily: "sahel",
        src: `url(data:font/otf;base64,${fonts.sahel})`,
      },
      {
        fontFamily: "myriad-pro",
        src: `url(data:font/otf;base64,${fonts["myriad-pro"]})`,
      },
      {
        fontFamily: "iran",
        src: `url(data:font/otf;base64,${fonts.iran})`,
      },
    ],
  },
  body: {
    margin: 0,
    padding: 0,
  },
  bodyTop: {
    height: (props) => `${props.headerFirstHeight}px`,
    verticalAlign: "top",
  },
  bodyCenter: {
    verticalAlign: "top",
  },
  bodyBottom: {
    height: (props) => `${props.footerLastHeight}px`,
    verticalAlign: "bottom",
  },
  root: {
    width: "100%",
    height: "100%",
    boxSizing: "border-box",
    // fontFamily: (props) => props.fontFamily,
    // position: "absolute",
  },
  paper: {
    width: "100%", //(props) => `${props.paperWidth}px`,
    height: "100%", //(props) => `${props.paperHeight - 1}px`,
    paddingLeft: (props) => `${props.marginLeft}px`,
    paddingRight: (props) => `${props.marginRight}px`,
    borderSpacing: "0px",
  },
  headerFirst: {
    position: "relative",
    height: (props) => `${props.headerFirstHeight}px`,
  },
  headerAll: {
    position: "relative",
    height: (props) => `${props.headerAllHeight}px`,
    marginTop: (props) => `${props.marginTop}px`,
  },
  content: {
    direction: "rtl",
    fontSize: (props) => `${props?.letterFontSize}px`,
  },
  footerAllRow: {
    position: "relative",
    height: (props) => `${props.footerAllHeight}px`,
  },
  blockFooterAll: {
    position: "relative",
    height: (props) => `${props.footerAllHeight}px`,
    marginBottom: (props) => `${props.marginBottom}px`,
    visibility: "hidden",
  },
  footerAll: {
    position: "relative",
    height: (props) => `${props.footerAllHeight}px`,
    marginBottom: (props) => `${props.marginBottom}px`,
    marginLeft: (props) => `${props.marginLeft}px`,
    marginRight: (props) => `${props.marginRight}px`,
  },
  footerLast: {
    position: "relative",
    height: (props) => `${props.footerLastHeight}px`,
  },
  background: {
    position: "fixed",
    backgroundImage: (props) => `url(data:image/png;base64,${props.background})`,
    backgroundPosition: "top center",
    backgroundSize: (props) => `${props.paperWidth}px ${props.paperHeight}px`,
    width: "100%",
    height: "100%",
    zIndex: "-10",
  },
  fixed: {
    position: "fixed",
    width: "100%",
    bottom: 0,
    left: 0,
    right: 0,
  },
  bodyHeight: {
    lineHeight: (props) => (props.fontFamily === "vazir" ? "2em" : `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`),
  },
});

const CustomEditor = ({ content }) => {
  try {
    if (Object.prototype.hasOwnProperty.call(content, "root")) {
      return <StaticEditor data={content} />;
    }
    return <Editor defaultValue={content} disabled />;
  } catch (error) {
    return "";
  }
};

const Body = ({ letter, property }) => {
  const classes = useStyles(property);
  const {
    allHeaderChildren,
    firstHeaderChildren,
    lastFooterChildren,
    allFooterChildren,
    signerNameHeight,
    headerAllHeight,
    headerFirstHeight,
    footerLastHeight,
    footerAllHeight,
  } = property;

  return (
    <div className={classes.body}>
      <div className={`${classes.headerAll} all-header`}>
        {Object.keys(allHeaderChildren).map((key, i) => {
          return components(key, allHeaderChildren, letter, property, {}, i);
        })}
      </div>

      <div className="all-footer">
        <div>
          <div className={classes.footerAll} id="footer">
            {Object.keys(allFooterChildren).map((key, i) => {
              return components(
                key,
                allFooterChildren,
                letter,
                property,
                { location: "allFooter", signerNameHeight },
                i
              );
            })}
          </div>
        </div>
      </div>

      <div className={classes.root}>
        <div className={classes.paper}>
          <div className={classes.headerFirst}>
            {Object.keys(firstHeaderChildren).map((key, i) => {
              return components(key, firstHeaderChildren, letter, property, {}, i);
            })}
          </div>
          <div className={classes.content}>
            <div className={classes.bodyHeight}>
              <CustomEditor content={letter.content} disabled />
            </div>
          </div>
          {hasShowableChildren(lastFooterChildren, letter) && (
            <div className={classes.footerLast}>
              {Object.keys(lastFooterChildren).map((key, i) => {
                return components(
                  key,
                  lastFooterChildren,
                  letter,
                  property,
                  { location: "lastFooter", signerNameHeight },
                  i
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WithBody(Body);
