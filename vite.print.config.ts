import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { viteSingleFile } from "vite-plugin-singlefile";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), viteSingleFile()],
  resolve: {
    alias: [
      { find: "@", replacement: path.resolve(__dirname, "./src") },
      ...["body"].map((dir) => ({
        find: dir,
        replacement: path.resolve(process.cwd(), `${dir}`),
      })),
    ],
  },
  build: {
    outDir: "dist/print",
    assetsInlineLimit: Infinity,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, "src/print/index.html"),
      },
    },
  },
});
