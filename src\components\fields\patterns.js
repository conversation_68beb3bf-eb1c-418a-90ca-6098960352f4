import config from "./config";

const localizeTitles = new Map([
  ["mr", { fa: "آقای", en: "Mr." }],
  ["mr_", { fa: "جناب آقای", en: "Mr." }],
  ["ms", { fa: "خانم", en: "Ms." }],
  ["ms_", { fa: "سرکار خانم", en: "Ms." }],
  ["dr", { fa: "دکتر", en: "Dr" }],
  ["eng", { fa: "مهندس", en: "Eng." }],
  ["mr_dr", { fa: "آقای دکتر", en: "Dr" }],
  ["mr_dr_", { fa: "جناب آقای دکتر", en: "Dr" }],
  ["ms_dr", { fa: "خانم دکتر", en: "Dr" }],
  ["ms_dr_", { fa: "سرکار خانم دکتر", en: "Dr" }],
  ["mr_eng", { fa: "آقای مهندس", en: "Eng." }],
  ["mr_eng_", { fa: "جناب آقای مهندس", en: "Eng." }],
  ["ms_eng", { fa: "خانم مهندس", en: "Eng." }],
  ["ms_eng_", { fa: "سرکار خانم مهندس", en: "Eng." }],
]);

const ll = (lang, data, key) => lang === 'en' ? data[`${key}_${lang}`] : data[key];

const namePattern = (token, lang, fallback = "") => {
  const pattern = token?.user
    ? [ll(lang, token?.user, 'firstName'), ll(lang, token?.user, 'lastName')]
    : [ll(lang, token, 'firstName'), ll(lang, token, 'lastName')]

  return joinPattern(pattern, ' ') || fallback;
};

const joinPattern = (segments, delimiter) => {
  if (Array.isArray(segments)) {
    return segments.filter(i => Boolean(i)).join(delimiter)
  }
  return "";
}

const orgPattern = (token, lang) => {
  let pattern = [];
  if (token?.user) {
    pattern = [ll(lang, token?.position, 'title'), ll(lang, token?.organization, 'title')]
  } else {
    pattern = [ll(lang, token, 'jobTitle'), ll(lang, token, 'business')]
  }
  return joinPattern(pattern, ' ');
};

const getLocalizationTitle = (token, lang) => {
  const title = (token?.user ? token?.user?.title : token?.title) || '';
  const localizeTitle = localizeTitles.get(title);
  if (!lang) {
    lang = 'fa';
  }
  return localizeTitle ? localizeTitle[lang] : title;
};

const getFallbackForNamePattern = (data, lang) => {
  if (data?.position) {
    return ll(lang, data.position, 'title');
  }
  return null;
}

const singleLinePattern = ( isTitleFullNameFirst, isFullNameOnly, data, lang) => {
  if(isFullNameOnly) {
    return [namePattern(data, lang, getFallbackForNamePattern(data, lang))];
  }

  let pattern = [ //default pattern for isTitleFullNameFirst === [0 or false]
    orgPattern(data, lang),
    getLocalizationTitle(data, lang),
    namePattern(data, lang, getFallbackForNamePattern(data, lang)),
  ]

  if (isTitleFullNameFirst === true || isTitleFullNameFirst === 1) {
    pattern = [
      getLocalizationTitle(data, lang),
      namePattern(data, lang, getFallbackForNamePattern(data, lang)),
      orgPattern(data, lang),
    ]
  }

  if (isTitleFullNameFirst === 2) {
    pattern = [
      orgPattern(data, lang),
      namePattern(data, lang, getFallbackForNamePattern(data, lang)),
    ]
  }

  if (isTitleFullNameFirst === 3) {
    pattern = [
      namePattern(data, lang, getFallbackForNamePattern(data, lang)),
      orgPattern(data, lang),
    ]
  }

  return joinPattern(pattern, config.PRINT_DEFAULT_SINGLE_LINE_DELIMITER)
}

const doubleLinePattern = (isTitleFullNameFirst, isFullNameOnly, data, lang) => {

  const orgPatternLine = orgPattern(data, lang);
  const isTitleAllowed = [0 , 1, true, false].includes(isTitleFullNameFirst);
  const namePatternLine = isFullNameOnly
    ? namePattern(data, lang, getFallbackForNamePattern(data, lang))
    : joinPattern([
      isTitleAllowed ? getLocalizationTitle(data, lang) : undefined,
        namePattern(data, lang, getFallbackForNamePattern(data, lang))
      ], ' ')

  let result = [ //default pattern for isTitleFullNameFirst === [0 or false]
    orgPatternLine,
    namePatternLine
  ]

  if (isTitleFullNameFirst === true || isTitleFullNameFirst === 1) {
    result = [namePatternLine, orgPatternLine]
  }

  if (isTitleFullNameFirst === 2) {
    result = [orgPatternLine, namePatternLine]
  }

  if (isTitleFullNameFirst === 3) {
    result = [namePatternLine, orgPatternLine]
  }

  return result
}

const nameFormatter = (
  dataToken,
  isSingleLine,
  isTitleFullNameFirst,
  isFullNameOnly,
  lang,
) => {
  try {
    const formatter = isSingleLine ? singleLinePattern : doubleLinePattern;
    return dataToken.map( item => formatter(
      isTitleFullNameFirst,
      isFullNameOnly,
      item,
      lang,
    ))
  } catch (e) {
    console.error(e);
    return [];
  }
};

const _lineFormatter = (token, lines, lang) => {
  const map = {
    personTitle: getLocalizationTitle(token, lang),
    firstName: token?.user ? ll(lang, token?.user, 'firstName') : ll(lang, token, 'firstName'),
    lastName: token?.user ? ll(lang, token?.user, 'lastName') : ll(lang, token, 'lastName'),
    jobTitle: token?.user ? ll(lang, token?.position, 'title') : ll(lang, token, 'jobTitle'),
    business: token?.user ? ll(lang, token?.organization, 'title') : ll(lang, token, 'business'),
  };
  const result = [];
  for (const line of lines) {
    result.push({
      segments: line?.segments
          ?.map((segment) => map[segment])
          ?.filter((segment) => Boolean(segment)),
      format: line?.format,
    });
  }
  return result;
};

const lineFormatter = (dataToken, lines, lang) => {
  try {
    return dataToken.map((item) => _lineFormatter(item, lines, lang));
  } catch (e) {
    console.error(e);
    return [];
  }
};

export { nameFormatter, lineFormatter };
