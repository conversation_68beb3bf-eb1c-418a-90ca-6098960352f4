import React, { useEffect } from "react";
import { Previewer } from "pagedjs";
import { renderToStaticMarkup } from "react-dom/server";
import { usePreview } from "./preview-provider";
// import setLayoutProperties from "../components/utils/setLayoutProperties";
import { toCanvas } from "html-to-image";
import Watermark from "../components/fields/watermark";
import { PagejsViewer } from "../pagedjs";

const Preview = ({ letter, layout }) => {
  const {
    images,
    setImages,
    currentPage,
    setCurrentPage,
    canvasRef,
    handleChangeLoading,
    loadingLayout,
    handleSetError,
  } = usePreview();

  const previewer = new Previewer();

  const runPagedPreview = (letterVal, layoutVal) => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (letterContainer) {
        letterContainer.innerHTML = "";
        letterContainer.style.fontFamily =
          layoutVal.paper?.fontFamily || "vazir";
        letterContainer.style.fontSize = `${
          layoutVal.paper?.fontSize || "14"
        }px`;
      }
      previewer.on("rendered", async (flow) => {
        console.log("testtest");

        const lastChild = letterContainer?.lastElementChild;
        if (!lastChild) return;
        const pages = lastChild?.querySelectorAll(".pagedjs_page");
        if (!pages || pages.length === 0) {
          handleSetError("No pages generated.");
          return;
        }
        Array.from(pages).forEach((page, index) => {
          const pageNumber = page.querySelector("#pageNumber");
          if (pageNumber) {
            pageNumber.textContent = `${flow.total} / ${index + 1}`;
          }
          page.style.position = "relative";
          const hasWatermark = page.querySelector(".watermark");
          if (!hasWatermark) {
            const wm = renderToStaticMarkup(<Watermark />);
            page.innerHTML += wm;
          }
        });
        const canvasPromises = Array.from(pages).map(async (page) => {
          const canvas = await toCanvas(page);
          return canvas.toDataURL();
        });
        const images = await Promise.all(canvasPromises);
        setImages(images);
        setCurrentPage((prev) =>
          Math.max(0, prev < flow.total ? prev : flow.total - 1)
        );
        setTimeout(() => {
          const letterBody = document.getElementById("letter-body");
          if (letterBody) letterBody.innerHTML = "";
          // if (letterContainer) letterContainer.innerHTML = "";
          handleChangeLoading(false);
          handleSetError(undefined);
        }, 500);
      });
    } catch (error) {
      console.error(error);
      handleSetError(error);
      handleChangeLoading(false);
    }
  };

  useEffect(() => {
    runPagedPreview(letter, layout);
  }, [layout, letter]);

  // Draw canvas image
  useEffect(() => {
    try {
      if (!images.length || currentPage >= images.length) return;
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext("2d");
      const image = new Image();

      image.onload = () => {
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(image, 0, 0);
      };

      image.src = images[currentPage];
      handleSetError(undefined);
    } catch (error) {
      console.error(error);
      handleSetError(error);
    }
  }, [images, currentPage]);

  return (
    <>
      <div
        style={{
          display: loadingLayout ? "none" : "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div style={{ height: "100%", backgroundColor: "white" }}>
          <canvas
            ref={canvasRef}
            style={{ maxWidth: "100%", backgroundColor: "white" }}
          />
        </div>
      </div>
      <PagejsViewer {...{ letter, layout, previewer }} />
    </>
  );
};

export default Preview;
