import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeTokenArray,
  tokenLinesHandler
} from "./common";
import config from "./config";
import { lineFormatter, nameFormatter } from "./patterns";

const useStyles = createUseStyles({
  customRtlField,
  sender: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.sender),
    "& span": {
      fontSize: `${props.sender.fontSize || config.PRINT_DEFAULT_FONT_SIZE}px`,
      fontWeight: props.sender.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.sender),
  tokenNodes: (props) => commonTokenNodes(props.sender),
});

const Sender = (props) => {
  const classes = useStyles({ sender: props.sender });
  const label = localizeLabel(props.sender);

  localizeTokenArray(props.sender, props?.letter?.sender);

  const senderArr = props?.sender?.lines
    ? lineFormatter(
      props?.letter?.sender,
      props?.sender?.lines,
      props?.sender?.lang,
    )
    // backward
    : nameFormatter(
      props?.letter?.sender,
      props?.sender?.isSingleLine,
      props?.sender?.isTitleFullNameFirst,
      props?.sender?.isFullNameOnly,
      props?.sender?.lang,
    );

  return (
    <div className={classes.bodyHeight}>
      <div className={classes.sender}>
        {
          label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>
          {senderArr.map((sender, i) =>
            Array.isArray(sender) ? (
              props?.sender?.lines
                ? tokenLinesHandler(sender)
                : sender.map((r, i) => <div key={i}>{r}</div>) // backward
            ) : (
              props?.sender?.lines
                ? tokenLinesHandler(sender)
                : <div key={i}>{sender}</div> // backward
            )
          )}
        </span>
      </div>
    </div>
  );
};

Sender.propTypes = {
  sender: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Sender;
