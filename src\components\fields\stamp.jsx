import React from "react";
import PropTypes from "prop-types";
import { createUseStyles } from "react-jss";
import config from "./config";
import { commonTokenFields } from "./common";

const useStyles = createUseStyles({
  stamp: (props) => ({
    position: "absolute",
    display: "flex",
    ...commonTokenFields(props.stamp),
    "& img": {
      maxWidth: `${props.stamp?.maxSize || 100}px`,
      maxHeight: `${props.stamp?.maxSize || 100}px`,
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
});

const Stamp = (props) => {
  const classes = useStyles({ stamp: props.stamp });
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.stamp}>
        {props.letter?.stamps && props.letter?.stamps.length > 0 ? (
          <img src={`data:image/png;base64,${props.letter.stamps[0]}`} alt="stamp" />
        ) : null}
      </div>
    </div>
  );
};

Stamp.propTypes = {
  letter: PropTypes.object.isRequired,
  stamp: PropTypes.any,
};

export default Stamp;
