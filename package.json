{"name": "com-chargoon-cloud-front-print", "version": "1.0.200", "main": "./dist/preview/Preview.cjs.js", "module": "./dist/preview/Preview.ems.js", "dependencies": {"@testing-library/jest-dom": "6.6.3", "@testing-library/user-event": "14.6.1", "com-chargoon-cloud-front-editor": "1.0.67", "com-chargoon-cloud-front-editor-2": "0.0.125", "html-to-image": "1.11.13", "lodash": "4.17.21", "pagedjs": "0.4.3", "web-vitals": "4.2.4"}, "scripts": {"start": "vite", "build": "vite build && npm run build-print", "build-print": "vite build --config vite.print.config.ts", "preview": "vite preview", "build:cjs": "babel src --out-dir dist/cjs --copy-files --env-name cjs", "build:esm": "babel src --out-dir dist/esm --copy-files --env-name esm", "build:types": "tsc --emitDeclarationOnly --declaration --outDir dist/types", "clean": "rm -rf dist", "build-babel": "npm run clean && npm run build:cjs && npm run build:esm && npm run build:types", "build-babel-ssr": "npx babel src -d dist --copy-files --presets=@babel/preset-env,@babel/preset-react,@babel/preset-typescript", "build-babel-2": "npx babel src -d dist --copy-files"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/cli": "7.27.0", "@babel/core": "7.26.10", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@testing-library/react": "16.3.0", "@types/jsdom": "21.1.7", "@vitejs/plugin-react": "4.4.1", "jsdom": "26.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-jss": "10.10.0", "vite": "5.4.18", "vite-plugin-singlefile": "^2.3.0"}, "peerDependencies": {"pagedjs": "0.4.3", "react": "18.3.1", "react-dom": "18.3.1"}}