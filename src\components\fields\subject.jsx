import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeToken
} from "./common";
import config from "./config";

const useStyles = createUseStyles({
  customRtlField,
  subject: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.subject),
    "& span": {
      fontSize: `${props.subject.fontSize || config.PRINT_DEFAULT_FONT_SIZE}px`,
      fontWeight: props.subject.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.subject),
  tokenNodes: (props) => commonTokenNodes(props.subject),
});

const Subject = (props) => {
  const classes = useStyles({ subject: props.subject });
  const label = localizeLabel(props.subject);
  const subject = localizeToken(props.subject, props.letter.subject);
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.subject}>
        {
          label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>{subject}</span>
      </div>
    </div>
  );
};

Subject.propTypes = {
  subject: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Subject;
