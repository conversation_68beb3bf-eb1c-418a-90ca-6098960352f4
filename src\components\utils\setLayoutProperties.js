export const setProperty = (variable, value, postfix = "") => {
  if (!value) return;
  document.documentElement.style.setProperty(variable, `${value}${postfix}`);
};

export const getProperty = (elem, prop) => {
  return elem.style.getPropertyValue(prop);
};

const getProperties = (layout) => {
  if (!layout) return new Map();

  const marginTop = layout?.margins?.top;
  const marginBottom = layout?.margins?.bottom;
  const marginLeft = layout?.margins?.left;
  const marginRight = layout?.margins?.right;
  const headerAllHeight = layout?.sections?.headers?.all?.height + marginTop;
  const footerAllHeight = layout?.sections?.footers?.all?.height + marginBottom;

  const pageWidth = layout?.paper?.width;
  const pageHeight = layout?.paper?.height;

  const properties = new Map();
  properties.set(
    "--page-background",
    layout?.paper?.background?.imageBase64
      ? `url(data:image/png;base64,${layout?.paper?.background?.imageBase64})`
      : "none"
  );

  properties.set("--pagedjs-margin-top", headerAllHeight + "cm");
  properties.set("--pagedjs-margin-bottom", footerAllHeight + "cm");
  properties.set("--pagedjs-margin-left", marginLeft + "cm");
  properties.set("--pagedjs-margin-right", marginRight + "cm");
  properties.set("--pagedjs-width", pageWidth + "cm");
  properties.set("--pagedjs-height", pageHeight + "cm");

  properties.set("--pagedjs-width-right", pageWidth + "cm");
  properties.set("--pagedjs-height-right", pageHeight + "cm");
  properties.set("--pagedjs-width-left", pageWidth + "cm");
  properties.set("--pagedjs-height-left", pageHeight + "cm");

  properties.set("--pagedjs-pagebox-width", pageWidth + "cm");
  properties.set("--pagedjs-pagebox-height", pageHeight + "cm");

  properties.set("--page-width", pageWidth + "cm");
  properties.set("--page-height", pageHeight + "cm");

  return properties;
};

export const getPropertiesString = (layout) => {
  if (!layout) return "";

  const properties = getProperties(layout);

  const result = Array.from(properties.entries())
    .map(([key, value]) => `${key}:${value}`)
    .join(";\n");

  return result;
};

const setLayoutProperties = (layout) => {
  if (!layout) return;

  const properties = getProperties(layout);

  properties.forEach((value, key) => {
    setProperty(key, value);
  });
};

export default setLayoutProperties;
