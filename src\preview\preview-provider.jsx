import React, { createContext, useContext, useState, useRef, useCallback } from "react";

const previewContext = createContext();

export const usePreview = () => useContext(previewContext);

export const PreviewProvider = ({ children }) => {
  const [images, setImages] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loadingLayout, setLoadingLayout] = useState(true);
  const [previewError, setPreviewError] = useState();
  const canvasRef = useRef(null);

  const handleChangeLoading = useCallback((value) => {
    setLoadingLayout(() => value);
  }, []);

  const handleSetError = useCallback((value) => {
    setPreviewError(() => value);
  }, []);

  const handlePrev = (e) => {
    e?.stopPropagation();
    setCurrentPage((prev) => Math.max(prev - 1, 0));
  };

  const handleNext = (e) => {
    e?.stopPropagation();
    setCurrentPage((prev) => Math.min(prev + 1, images.length - 1));
  };

  return (
    <previewContext.Provider
      value={{
        images,
        setImages,
        currentPage,
        setCurrentPage,
        canvasRef,
        handleNext,
        handlePrev,
        totalPage: images?.length || 1,
        loadingLayout,
        handleChangeLoading,
        previewError,
        handleSetError,
      }}
    >
      {children}
    </previewContext.Provider>
  );
};
