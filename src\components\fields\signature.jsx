import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import SignerName from "./signerName";
import config from "./config";
import { isNumeric } from "./common";

const useStyles = createUseStyles({
  root: (props) => ({
    position: "absolute",
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap-reverse",
    right: props?.signature?.right || isNumeric(props?.signature?.right) ? `${props?.signature?.right}px` : "unset",
    left: props?.signature?.left || isNumeric(props?.signature?.left) ? `${props?.signature?.left}px` : "unset",
    top: props.signature.top ? `${props.signature.top}px` : null,
    bottom: props.signature.bottom ? `${props.signature.bottom}px` : null,
  }),
  signature: (props) => ({
    direction: "rtl",
    padding: "5px",
    textAlign: props.signature?.align || "start",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  }),
  signatureImg: (props) => ({
    // objectFit:'contain',
    maxWidth: `${props.signature?.maxSize || 100}px`,
    maxHeight: `${props.signature?.maxSize || 100}px`,
    zIndex: 0,
  }),
  signatureBlank: () => ({
    height: `80px`,
  }),
  signatureName: (props) => ({
    position: "absolute",
    zIndex: props.signature?.signerNameOnTop ? 1 : -1,
    marginTop:
      props.signature?.signerNameDirection === "up" || props.signature?.signerNameDirection === "down"
        ? props.signature?.signerNameDirection === "up"
          ? `-${props.signature?.signerNameOffset}px`
          : `${props.signature?.signerNameOffset}px`
        : "0px",
    marginRight:
      props.signature?.signerNameDirection === "right" || props.signature?.signerNameDirection === "left"
        ? props.signature?.signerNameDirection === "right"
          ? `-${props.signature?.signerNameOffset}px`
          : `${props.signature?.signerNameOffset}px`
        : "0px",
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
});

// this function checks if the element is gonna go outside the page and into the second page from the downside of the page
function checkIfTheElementIsContained(props, allContainerHeight) {
  const intrusion =
    Number(props.signature?.maxSize) +
    Number(props.options.signerNameHeight || 0) -
    (allContainerHeight - props.signature.top);
  const bottom = intrusion > 0 ? "0" : null;
  return { intrusion, bottom };
}

const Signature = (props) => {
  const { letter, signature, property, options } = props;
  let allContainerHeight;
  let top = props.signature.top;
  let bottom;
  let result;

  if (!signature?.signerNameOnTop) {
    signature.signerNameOnTop = false;
  }
  if (!signature?.signerNameOffset) {
    signature.signerNameOffset = 1.5; //cm
  }
  if (!signature?.signerNameDirection) {
    signature.signerNameDirection = "up";
  }

  const isNotNullArr = (arr) => arr && arr.length > 0;

  // TODO: - Use left position for horizontal signer
  if (isNotNullArr(property.signatureItemsWidth)) {
    const widthItems = property.signatureItemsWidth.reduce((p, a) => p + a, 0);
    const wToken = 50;
    const eToken = widthItems - wToken;
    props.signature.right = Math.max(props.signature.right - eToken * 0.5, 0);
  }

  switch (options?.location) {
    case "allFooter":
      allContainerHeight = property.footerAllHeight;
      result = checkIfTheElementIsContained(props, allContainerHeight);
      bottom = result.bottom;
      if (bottom) top = null;
      break;

    case "lastFooter":
      allContainerHeight = property.footerAllHeight + property.footerLastHeight;
      result = checkIfTheElementIsContained(props, allContainerHeight);
      bottom = result.bottom;
      if (bottom) {
        top -= result.intrusion;
        bottom = null;
      }
      break;
    default:
      bottom = null;
      break;
  }

  const classes = useStyles({ signature: { ...signature, top, bottom } });

  return (
    <div className={classes.bodyHeight}>
      <div className={classes.root} id={"signatureRootToken"}>
        {letter.signedBy?.length > 0 &&
          letter.signedBy.map((sign, i) => (
            <div
              key={i}
              className={classes.signature}
              style={{ width: `${isNotNullArr(property.signatureItemsWidth) ? property.signatureItemsWidth[i] : 0}px` }}
              id={`signatureItemToken_${i}`}
            >
              {sign.signature ? (
                <img
                  id={`signatureImageItemToken_${i}`}
                  alt="signature"
                  src={`data:image/png;base64,${sign.signature}`}
                  className={classes.signatureImg}
                />
              ) : (
                <span className={classes.signatureBlank}></span>
              )}
              {(signature.signerName || signature.isSignerName) && (
                <span className={classes.signatureName} id={`signatureNameItemToken_${i}`}>
                  <SignerName signature={signature} letter={letter} signInfo={sign} />
                </span>
              )}
            </div>
          ))}
      </div>
    </div>
  );
};

Signature.propTypes = {
  signature: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Signature;
