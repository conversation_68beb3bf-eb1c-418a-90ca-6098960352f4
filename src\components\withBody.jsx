import React from "react";
import { c2pInObject } from "./utils";

function getRecipientInSection(layout) {
  for (const l of ["headers", "footers"]) {
    const section = layout?.sections[l];
    for (const sectionKey of Object.keys(section)) {
      if (section[sectionKey]?.children?.recipient) {
        const recipient = section[sectionKey]?.children?.recipient;

        const childrens = Object.keys(section[sectionKey].children);
        const nextTokens = [];
        for (const c of childrens) {
          if (c === "recipient") {
            continue;
          }
          const token = section[sectionKey].children[c];
          if (token.top >= recipient.top) {
            nextTokens.push(token);
          }
        }
        return {
          recipient,
          section: l,
          position: sectionKey,
          nextTokens,
        };
      }
    }
  }
  return null;
}

function recipientFixHeight(letter, layout, extra) {
  const t = getRecipientInSection(layout);
  if (t && letter.recipient.length > 1) {
    let v;
    if (extra?.recipientBounds?.height) {
      v = extra.recipientBounds.height - extra.recipientBounds.height / letter.recipient.length;
    } else {
      // manual height detection
      const fontsHeight = {
        vazir: { l: 12, h: 30 },
        lotus: { l: 15, h: 39 },
        mitra: { l: 13, h: 33 },
        nazanin: { l: 14, h: 35 },
        yekan: { l: 14, h: 35 },
        yaghot: { l: 14, h: 36 },
        yaghotBold: { l: 14, h: 36 },
        sahel: { l: 12, h: 30 },
        iran: { l: 13, h: 32 }, 
        "b-traffic": { l: 12, h: 30 },
        "b-titr": { l: 10, h: 28 }, 
        "b-zar": { l: 13, h: 34 }, 
        "myriad-pro": { l: 12, h: 30 },
      };
      const baseFontSize = 14.0;
      const fontScale = t.recipient.fontSize / baseFontSize;
      const l = fontsHeight[layout.paper.fontFamily].l * fontScale;
      const h = fontsHeight[layout.paper.fontFamily].h * fontScale;
      v = t.recipient.isSingleLine ? letter.recipient.length * l : letter.recipient.length * h;
    }
    for (const nextToken of t.nextTokens) {
      nextToken.top += v;
    }
    return {
      section: t.position + "_" + t.section,
      height: v,
    };
  }
  return null;
}

const WithBody = (Component) => (props) => {
  let { letter, layout, extra } = props;

  //! Remember | Error Maker --> Pay attention to those what should not be converted
  layout = c2pInObject(JSON.parse(JSON.stringify(layout)), ["fontSize", "letterFontSize", "isTitleFullNameFirst"]);

  //* Page Size
  const paperHeight = layout.paper.height;
  const paperWidth = layout.paper.width;

  //* Margins
  const marginTop = layout.margins.top;
  const marginBottom = layout.margins.bottom;
  const marginLeft = layout.margins.left;
  const marginRight = layout.margins.right;

  //* Section Height
  let headerAllHeight = layout.sections.headers.all.height;
  let headerFirstHeight = layout.sections.headers.first.height;
  let footerLastHeight = layout.sections.footers.last.height;
  let footerAllHeight = layout.sections.footers.all.height;

  const fixHeight = recipientFixHeight(letter, layout, extra);
  if (fixHeight) {
    switch (fixHeight.section) {
      case "first_headers":
        headerFirstHeight += fixHeight.height;
        break;
      case "all_headers":
        headerAllHeight += fixHeight.height;
        break;
      case "last_footers":
        footerLastHeight += fixHeight.height;
        break;
      case "all_footers":
        footerAllHeight += fixHeight.height;
        break;
    }
  }
  // possibly useless , it's for the time that the height of the content was constant
  const contentHeight =
    paperHeight - (headerAllHeight + headerFirstHeight + footerLastHeight + footerAllHeight + marginBottom + marginTop);

  const property = {
    paperHeight,
    paperWidth,
    headerAllHeight,
    headerFirstHeight,
    footerLastHeight,
    footerAllHeight,
    marginTop,
    marginBottom,
    marginLeft,
    marginRight,
    contentHeight,
    signerNameHeight: extra?.signerNameBounds?.height,
    signatureRootWidth: extra?.signatureRootBounds?.width,
    signatureItemsWidth: extra?.signatureItemsBounds,
    allHeaderChildren: layout.sections.headers.all.children,
    firstHeaderChildren: layout.sections.headers.first.children,
    lastFooterChildren: layout.sections.footers.last.children,
    allFooterChildren: layout.sections.footers.all.children,
    background: layout.paper.background.imageBase64,
    fontFamily: layout.paper.fontFamily,
    letterFontSize: layout.paper?.letterFontSize || 14,
  };

  return <Component letter={letter} property={property} />;
};

export default WithBody;
