import React from "react";
import { createUseStyles } from "react-jss";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeTokenArray,
  tokenLinesHandler
} from "./common";
import PropTypes from "prop-types";

import config from "./config";
import { lineFormatter, nameFormatter } from "./patterns";

const useStyles = createUseStyles({
  customRtlField,
  cc: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.cc),
    "& span": {
      fontSize: `${props.cc.fontSize || config.PRINT_DEFAULT_FONT_SIZE}px`,
      fontWeight: props.cc.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.cc),
  tokenNodes: (props) => commonTokenNodes(props.cc),
  noteRoot: {
    display: "flex",
    alignItems: "flex-end",
  }
});

const CC = (props) => {
  const classes = useStyles({ cc: props.cc });
  const label = localizeLabel(props.cc);

  localizeTokenArray(props.cc, props?.letter?.cc);

  const ccArr = props?.cc?.lines
    ? lineFormatter(
      props?.letter?.cc,
      props?.cc?.lines,
      props?.cc?.lang,
    )
    // backward
    : nameFormatter(
      props?.letter?.cc,
      props?.cc?.isSingleLine,
      props?.cc?.isTitleFullNameFirst,
      props?.cc?.isFullNameOnly,
      props?.cc?.lang,
    );

  let t = 0;
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.cc}>
        {
          (Array.isArray(ccArr) ? ccArr.length > 0 : ccArr) && label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>
         {
           props?.cc?.lines
             ? ccArr.map((cc, i) =>
               <div key={i} className={classes.noteRoot}>
                 <span>{tokenLinesHandler(cc)}</span>
                 {props?.letter?.cc[i]?.note && <span>، {props?.letter?.cc[i]?.note}</span>}
               </div>
             )
             : ccArr
               .reduce((r, c) => (Array.isArray(c) ? [...r, ...c] : [...r, c]), [])
               .map((cc, i) =>
                 props?.cc?.isSingleLine || false
                   ? // single line
                   props?.letter?.cc[i]?.note
                     ? (<div key={i}>{cc}، {props?.letter?.cc[i]?.note}</div>)
                     : (<div key={i}>{cc}</div>)
                   : // multi line
                   i % 2 !== 0 && props?.letter?.cc[i % ++t]?.note
                     ? (<div key={i}>{cc}، {props?.letter?.cc[i % t]?.note}</div>)
                     : (<div key={i}>{cc}</div>)
               )

         }
        </span>
      </div>
    </div>
  );
};

CC.propTypes = {
  cc: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default CC;
