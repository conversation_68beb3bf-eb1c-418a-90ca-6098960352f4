import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import { commonTokenFields, customRtlField, localizeLabel } from "./common";
import config from "./config";

const useStyles = createUseStyles({
  customRtlField,
  staticText: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.staticText),
    fontSize: `${
      props.staticText.fontSize || config.PRINT_DEFAULT_FONT_SIZE
    }px`,
    fontWeight: props.staticText.fontWeight || "normal",
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  }
});

const StaticText = (props) => {
  const classes = useStyles({ staticText: props.staticText });
  const label = localizeLabel(props.staticText);
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.staticText}>{label.trim()}</div>
    </div>
  );
};

StaticText.propTypes = {
  staticText: PropTypes.object,
};

export default StaticText;
