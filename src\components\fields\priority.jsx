import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";

import config from "./config";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeToken
} from "./common";

export const translatePriority = (word, lang) => {
  const priority = {
    عادی: { fa: 'عادی', en: 'Normal' },
    فوری: { fa: 'فوری', en: 'Urgent' },
    آنی: { fa: 'آنی', en: 'Immediate' },
    normal: { fa: 'عادی', en: 'Normal' },
    urgent: { fa: 'فوری', en: 'Urgent' },
    immediate: { fa: 'آنی', en: 'Immediate' },
  };
  return priority[word] ? priority[word][lang] : word;
};

const useStyles = createUseStyles({
  customRtlField,
  priority: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.priority),
    "& span": {
      fontSize: `${
        props.priority.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.priority.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.priority),
  tokenNodes: (props) => commonTokenNodes(props.priority),
});

const Priority = (props) => {
  const classes = useStyles({ priority: props.priority });
  const label = localizeLabel(props.priority);
  let priority = localizeToken(props.priority, props.letter.priority);
  priority = translatePriority(priority, props.priority?.lang || 'fa');
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.priority}>
        {
          priority && label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>{priority}</span>
      </div>
    </div>
  );
};

Priority.propTypes = {
  priority: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Priority;
