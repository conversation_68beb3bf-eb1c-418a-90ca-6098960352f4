import React from "react";
import { convertDigitsToPersian, convertDigitsToPersianDeepArray } from "../utils/convertDigitsToPersian";

export const customRtlField = {
  position: "absolute",
  display: "flex",
  direction: "rtl",
  gap: "0.2rem",
};

export const isNumeric = (v) => !isNaN(v) && !isNaN(parseFloat(v))

export const commonTokenFields = (fieldProps) => ({
  right: fieldProps?.right || isNumeric(fieldProps?.right) ? `${fieldProps?.right}px` : "unset",
  left: fieldProps?.left || isNumeric(fieldProps?.left) ? `${fieldProps?.left}px` : "unset",
  textAlign: fieldProps?.align || "inherit",
  top: fieldProps?.top ? `${fieldProps?.top}px` : 0,
  flexFlow: fieldProps?.flow === 'left' ? 'row-reverse' : 'row',
});

export const commonTokenLabel = (fieldProps) => ({
  flexFlow: fieldProps?.flow === "left" ? "row-reverse" : "row",
  display: "flex",
});

export const commonTokenNodes = (fieldProps) => ({
  direction: fieldProps?.direction || "rtl",
});

export const tokenLinesHandler = (fieldProps) => fieldProps.map((r, i) => <div style={{
  direction: r?.format?.direction || "inherit",
  textAlign: r?.format?.align || "inherit"
}} key={i}>{r.segments.join(' ')}</div>);

export const localizeLabel = (fieldProps) => fieldProps?.lang === "en"
  ? fieldProps?.label
  : convertDigitsToPersian(fieldProps?.label);

export const localizeToken = (fieldProps, data) => fieldProps?.lang === "en"
  ? data
  : convertDigitsToPersian(data);

export const localizeTokenArray = (fieldProps, data) => {
  if (fieldProps?.lang !== "en") {
    convertDigitsToPersianDeepArray(data);
  }
}
