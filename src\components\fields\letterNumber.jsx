import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
} from "./common";
import config from "./config";
import { formatLetterNumber } from "../utils";
import { convertDigitsToPersian } from "../utils/convertDigitsToPersian";

const useStyles = createUseStyles({
  customRtlField,
  letterNumber: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.letterNumber),
    "& span": {
      fontSize: `${
        props.letterNumber.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.letterNumber.fontWeight || "normal",
    },
  }),
  ltr: { direction: "ltr" },
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.letterNumber),
  tokenNodes: (props) => commonTokenNodes(props.letterNumber),
});

const LetterNumber = (props) => {
  const classes = useStyles({ letterNumber: props.letterNumber });
  const label = localizeLabel(props.letterNumber);
  let letterNumber = formatLetterNumber(props.letter.letterNumber);
  if(props.letterNumber?.lang === "fa"){
    letterNumber =  convertDigitsToPersian(letterNumber);
  }
  let fontFamily = props.fontFamily;
  let useEnglishDigits = props.letterNumber?.useEnglishDigits || false;
  return (
    <div className={classes.bodyHeight} style={{ fontFamily: useEnglishDigits ? `vazir_wd` : fontFamily }}>
      <div className={classes.letterNumber}>
        {
          letterNumber && label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>
          <span className={classes.ltr}>{letterNumber}</span>
        </span>
      </div>
    </div>
  );
};

LetterNumber.propTypes = {
  letterNumber: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default LetterNumber;
