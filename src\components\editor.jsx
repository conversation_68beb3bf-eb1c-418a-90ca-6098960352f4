import React from "react";

import Editor from "com-chargoon-cloud-front-editor";
import { StaticEditor } from "com-chargoon-cloud-front-editor-2";
import "com-chargoon-cloud-front-editor/lib/index.css";

export const CustomEditor = ({ content, extra }) => {
  try {
    if (Object.prototype.hasOwnProperty.call(content, "root")) {
      return <StaticEditor data={content} />;
    }
    return <Editor defaultValue={content} disabled />;
  } catch (error) {
    return "";
  }
};
