const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];

const convertDigitsToPersian = (text) => {
  if (typeof text === "string") {
    return text.replace(/[0-9]/g, (digit) => persianDigits[digit]);
  } else {
    return text;
  }
};

const convertDigitsToPersianDeepObject = (obj) => {
  if (typeof obj === "number" || typeof obj === "string") {
    return convertDigitsToPersian(obj);
  }
  Object.keys(obj).forEach((key) => {
    if (typeof obj[key] === "object" && obj[key] !== null) {
      return convertDigitsToPersianDeepObject(obj[key]);
    }
    if (typeof obj[key] === "number" || typeof obj[key] === "string") {
      obj[key] = convertDigitsToPersian(obj[key]);
    }
  });
};

const convertDigitsToPersianDeepArray = (obj) => {
  if (obj && Array.isArray(obj)) {
    obj.forEach((o) => convertDigitsToPersianDeepObject(o));
  }
};

export { convertDigitsToPersian, convertDigitsToPersianDeepObject, convertDigitsToPersianDeepArray };
