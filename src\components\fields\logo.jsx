import React from "react";
import PropTypes from "prop-types";
import { createUseStyles } from "react-jss";
import config from "./config";
import { commonTokenFields } from "./common";

const useStyles = createUseStyles({
  logo: (props) => ({
    position: "absolute",
    display: "flex",
    ...commonTokenFields(props.logo),
    "& img": {
      maxWidth: `${props.logo?.maxSize || 100}px`,
      maxHeight: `${props.logo?.maxSize || 100}px`,
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  }
});

const Logo = (props) => {
  const classes = useStyles({ logo: props.logo });
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.logo}>
        {props.letter.logo ? (
            <img src={`data:image/png;base64,${props.letter.logo}`} alt="logo" />
        ) : null}
      </div>
    </div>
  );
};

Logo.propTypes = {
  letter: PropTypes.object.isRequired,
  logo: PropTypes.any,
};

export default Logo;
