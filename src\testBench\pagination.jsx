import { usePreview } from "../preview/preview-provider";

const Pagination = () => {
  const { handleNext, handlePrev, currentPage, images } = usePreview();
  return (
    <div style={{ marginBottom: "1rem" }}>
      <button onClick={handlePrev} disabled={currentPage === 0}>
        ← Prev
      </button>
      <span style={{ margin: "0 1rem" }}>
        Page {currentPage + 1} of {images.length}
      </span>
      <button onClick={handleNext} disabled={currentPage >= images.length - 1}>
        Next →
      </button>
    </div>
  );
};

export default Pagination;
