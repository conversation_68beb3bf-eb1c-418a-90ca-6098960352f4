import fonts from "./fonts";
import fonts_wd from "./fonts_wd";

// Create font CSS for PagedJS
export const createFontCSS = () => {
  const fontFaces = [
    { name: "vazir", data: fonts.vazir },
    { name: "vazir_wd", data: fonts_wd.vazir },
    { name: "lotus", data: fonts.lotus },
    { name: "mitra", data: fonts.mitra },
    { name: "yekan", data: fonts.yekan },
    { name: "nazanin", data: fonts.nazanin },
    { name: "yaghot", data: fonts.yaghot },
    { name: "yaghotBold", data: fonts.yaghotBold },
    { name: "b-titr", data: fonts["b-titr"] },
    { name: "b-traffic", data: fonts["b-traffic"] },
    { name: "b-zar", data: fonts["b-zar"] },
    { name: "sahel", data: fonts.sahel },
    { name: "myriad-pro", data: fonts["myriad-pro"] },
    { name: "iran", data: fonts.iran },
  ];

  return fontFaces
    .filter(font => font.data) // Only include fonts that exist
    .map(font => `
      @font-face {
        font-family: "${font.name}";
        src: url(data:font/otf;base64,${font.data});
      }
    `).join('\n');
};
