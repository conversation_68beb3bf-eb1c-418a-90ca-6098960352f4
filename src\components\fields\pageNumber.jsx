import React, { useEffect } from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import { commonTokenFields, customRtlField, localizeToken } from "./common";

import config from "./config";
import { convertDigitsToPersian } from "../utils/convertDigitsToPersian";

const useStyles = createUseStyles({
  customRtlField,
  pageNumber: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.pageNumber),
    "& span::before": {
      fontSize: `${
        props.pageNumber.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.pageNumber.fontWeight || "normal",
      fontFamily:
        props.pageNumber.lang === "en"
          ? props.pageNumber.fontFamily
          : "vazir",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
});

const PageNumber = (props) => {
  const classes = useStyles({
    pageNumber: props.pageNumber,
  });

  return (
    <div className={classes.bodyHeight}>
      <div className={classes.pageNumber}>
        <span
          id="pageNumber"
          className={
            props.pageNumber.lang === "en" ? "counter-en" : "counter-fa"
          }
        ></span>
      </div>
    </div>
  );
};

PageNumber.propTypes = {
  currentPage: PropTypes.any,
  totalPages: PropTypes.any,
  pageNumber: PropTypes.any,
};

export default PageNumber;
