import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeToken
} from "./common";
import config from "./config";

const useStyles = createUseStyles({
  customRtlField,
  date: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.date),
    "& span": {
      fontSize: `${props.date.fontSize || config.PRINT_DEFAULT_FONT_SIZE}px`,
      fontWeight: props.date.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.date),
  tokenNodes: (props) => commonTokenNodes(props.date),
});

const Date = (props) => {
  const classes = useStyles({ date: props.date });
  const label = localizeLabel(props.date);
  const date = localizeToken(props.date, props?.letter?.date);
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.date}>
        {
          label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>{date}</span>
      </div>
    </div>
  );
};

Date.propTypes = {
  date: PropTypes.any,
  letter: PropTypes.object.isRequired,
};

export default Date;
