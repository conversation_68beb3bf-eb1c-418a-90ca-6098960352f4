import React from "react";
import { createUseStyles } from "react-jss";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
} from "./common";
import PropTypes from "prop-types";
import config from "./config";

const useStyles = createUseStyles({
  customRtlField,
  attachment: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.attachment),
    "& span": {
      fontSize: `${
        props.attachment.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.attachment.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.attachment),
  tokenNodes: (props) => commonTokenNodes(props.attachment),
});

const Attachment = (props) => {
  const classes = useStyles({ attachment: props.attachment });
  const label = localizeLabel(props.attachment);
  const isEnglish = props?.attachment?.lang === "en";
  const attachment = props.letter.hasAttachment
    ? (isEnglish ? "Yes" : "دارد")
    : (isEnglish ? "No" : "ندارد");
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.attachment}>
        {
          label?.trim() &&
            <span className={classes.label}>
              <span className={classes.tokenNodes} id="label">{label}</span>
              <span>:</span>
            </span>
        }
        <span className={classes.tokenNodes}>{attachment}</span>
      </div>
    </div>
  );
};

Attachment.propTypes = {
  attachment: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Attachment;
