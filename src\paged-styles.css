html {
  -webkit-print-color-adjust: "exact";
  -webkit-text-size-adjust: none;
}
table,
figure {
  margin: 0;
  padding: 0;
  border: 0;
  text-size-adjust: none;
  -webkit-text-size-adjust: none;
  font-size: inherit;
}
table,
figure {
  margin: 0;
  padding: 0;
  border: 0;
}
.alignment-DraftStyleDefault-alignLeft {
  text-align: left;
}
.alignment-DraftStyleDefault-alignCenter {
  text-align: center;
}
.alignment-DraftStyleDefault-alignRight {
  text-align: right;
}
.alignment-DraftStyleDefault-alignJustify {
  text-align: justify;
}
.alignment-DraftStyleDefault-rtl {
  direction: rtl;
}
.alignment-DraftStyleDefault-ltr {
  direction: ltr;
}

.all-header {
  position: running(AH);
}

.all-footer {
  position: running(AF);
}

@page {
  size: A4;
  background: var(--page-background) no-repeat center center;
  background-size: contain;

  @top-center {
    content: element(AH);
  }
  @bottom-center {
    content: element(AF);
  }
}
