import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import {
  commonTokenFields,
  commonTokenLabel,
  commonTokenNodes,
  customRtlField,
  localizeLabel,
  localizeToken,
} from "./common";
import config from "./config";

const translateConfidentiality = (word, lang) => {
  const confidentiality = {
    عادی: { fa: "عادی", en: "Normal" },
    محرمانه: { fa: "محرمانه", en: "Secret" },
    سری: { fa: "سری", en: "Top Secret" },
    normal: { fa: "عادی", en: "Normal" },
    secret: { fa: "محرمانه", en: "Secret" },
    "top-secret": { fa: "سری", en: "Top Secret" },
  };
  return confidentiality[word] ? confidentiality[word][lang] : word;
};

const useStyles = createUseStyles({
  customRtlField,
  confidentiality: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.confidentiality),
    "& span": {
      fontSize: `${props.confidentiality.fontSize || config.PRINT_DEFAULT_FONT_SIZE}px`,
      fontWeight: props.confidentiality.fontWeight || "normal",
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  },
  label: (props) => commonTokenLabel(props.confidentiality),
  tokenNodes: (props) => commonTokenNodes(props.confidentiality),
});

const Confidentiality = (props) => {
  const classes = useStyles({ confidentiality: props.confidentiality });
  const label = localizeLabel(props.confidentiality);
  let confidentiality = localizeToken(props.confidentiality, props?.letter?.confidentiality);
  confidentiality = translateConfidentiality(confidentiality, props.confidentiality?.lang || "fa");
  return (
    <div className={classes.bodyHeight}>
      <div className={classes.confidentiality}>
        {confidentiality && label?.trim() && (
          <span className={classes.label}>
            <span className={classes.tokenNodes} id="label">
              {label}
            </span>
            <span>:</span>
          </span>
        )}
        <span className={classes.tokenNodes}>{confidentiality}</span>
      </div>
    </div>
  );
};

Confidentiality.propTypes = {
  confidentiality: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Confidentiality;
