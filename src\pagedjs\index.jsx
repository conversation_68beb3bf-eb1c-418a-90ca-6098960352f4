import Body from "../components/body";
import { useEffect } from "react";
import setLayoutProperties from "../components/utils/setLayoutProperties";
import { Previewer } from "pagedjs";
import { CreateCssBlob } from "../components/utils";
import { renderToStaticMarkup } from "react-dom/server";
import { JssProvider } from "react-jss";
import fonts from "../components/fonts";
import fonts_wd from "../components/fonts_wd";

const cssUrl = CreateCssBlob();

// Create font CSS for PagedJS
const createFontCSS = () => {
  const fontFaces = [
    { name: "vazir", data: fonts.vazir },
    { name: "vazir_wd", data: fonts_wd.vazir },
    { name: "lotus", data: fonts.lotus },
    { name: "mitra", data: fonts.mitra },
    { name: "yekan", data: fonts.yekan },
    { name: "nazanin", data: fonts.nazanin },
    { name: "yaghot", data: fonts.yaghot },
    { name: "yaghotBold", data: fonts.yaghotBold },
    { name: "b-titr", data: fonts["b-titr"] },
    { name: "b-traffic", data: fonts["b-traffic"] },
    { name: "b-zar", data: fonts["b-zar"] },
    { name: "sahel", data: fonts.sahel },
    { name: "myriad-pro", data: fonts["myriad-pro"] },
    { name: "iran", data: fonts.iran },
  ];

  return fontFaces
    .filter(font => font.data) // Only include fonts that exist
    .map(font => `
      @font-face {
        font-family: "${font.name}";
        src: url(data:font/otf;base64,${font.data});
      }
    `).join('\n');
};

// Create CSS blob with fonts
const createFontCSSBlob = () => {
  const fontCSS = createFontCSS();
  const cssBlob = new Blob([fontCSS], { type: "text/css" });
  return URL.createObjectURL(cssBlob);
};

const fontCssUrl = createFontCSSBlob();

export const PagejsViewer = ({
  letter,
  layout,
  previewer = new Previewer(),
}) => {
  useEffect(() => {
    setTimeout(async () => {
      const source = document.getElementById("letter-body");
      const letterContainer = document.getElementById("letter-preview");

      if (letterContainer) {
        letterContainer.style.fontFamily = layout.paper.fontFamily;
      }

      setLayoutProperties(layout);
      // Pass both CSS files to PagedJS - fonts first, then layout styles
      await previewer.preview(source, [fontCssUrl, cssUrl], letterContainer);
      source.remove();
    }, 500);
  }, [letter, layout]);

  return (
    <>
      <JssProvider>
        <div id="letter-body">
          <Body letter={letter} layout={layout} />
        </div>
        <div id="letter-preview"></div>
      </JssProvider>
    </>
  );
};
