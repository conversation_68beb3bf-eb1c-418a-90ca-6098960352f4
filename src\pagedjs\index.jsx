import Body from "../components/body";
import { useEffect } from "react";
import setLayoutProperties from "../components/utils/setLayoutProperties";
import { Previewer } from "pagedjs";
import { CreateCssBlob } from "../components/utils";
import { renderToStaticMarkup } from "react-dom/server";
import { JssProvider } from "react-jss";
import fonts from "../components/fonts";
import fonts_wd from "../components/fonts_wd";

const cssUrl = CreateCssBlob();

export const PagejsViewer = ({
  letter,
  layout,
  previewer = new Previewer(),
}) => {
  useEffect(() => {
    setTimeout(async () => {
      const source = document.getElementById("letter-body");
      const letterContainer = document.getElementById("letter-preview");

      if (letterContainer) {
        letterContainer.style.fontFamily = layout.paper.fontFamily;
      }

      // Create and inject font styles directly into the document
      const fontFaces = [
        { name: "vazir", data: fonts.vazir },
        { name: "vazir_wd", data: fonts_wd.vazir },
        { name: "lotus", data: fonts.lotus },
        { name: "mitra", data: fonts.mitra },
        { name: "yekan", data: fonts.yekan },
        { name: "nazanin", data: fonts.nazanin },
        { name: "yaghot", data: fonts.yaghot },
        { name: "yaghotBold", data: fonts.yaghotBold },
        { name: "b-titr", data: fonts["b-titr"] },
        { name: "b-traffic", data: fonts["b-traffic"] },
        { name: "b-zar", data: fonts["b-zar"] },
        { name: "sahel", data: fonts.sahel },
        { name: "myriad-pro", data: fonts["myriad-pro"] },
        { name: "iran", data: fonts.iran },
      ];

      const fontCSS = fontFaces
        .filter(font => font.data)
        .map(font => `
          @font-face {
            font-family: "${font.name}";
            src: url(data:font/otf;base64,${font.data});
          }
        `).join('\n');

      // Remove existing font style if any
      const existingFontStyle = document.getElementById('pagedjs-fonts');
      if (existingFontStyle) {
        existingFontStyle.remove();
      }

      // Inject font styles
      const fontStyleElement = document.createElement('style');
      fontStyleElement.id = 'pagedjs-fonts';
      fontStyleElement.textContent = fontCSS;
      document.head.appendChild(fontStyleElement);

      setLayoutProperties(layout);
      await previewer.preview(source, [cssUrl], letterContainer);
      source.remove();
    }, 500);

    // Cleanup function
    return () => {
      const fontStyleElement = document.getElementById('pagedjs-fonts');
      if (fontStyleElement) {
        fontStyleElement.remove();
      }
    };
  }, [letter, layout]);

  return (
    <>
      <JssProvider>
        <div id="letter-body">
          <Body letter={letter} layout={layout} />
        </div>
        <div id="letter-preview"></div>
      </JssProvider>
    </>
  );
};
