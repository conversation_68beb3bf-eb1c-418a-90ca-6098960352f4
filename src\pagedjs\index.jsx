import Body from "../components/body";
import { useEffect } from "react";
import setLayoutProperties from "../components/utils/setLayoutProperties";
import { Previewer } from "pagedjs";
import { CreateCssBlob } from "../components/utils";
import { renderToStaticMarkup } from "react-dom/server";
import { JssProvider } from "react-jss";

const cssUrl = CreateCssBlob();

export const PagejsViewer = ({
  letter,
  layout,
  previewer = new Previewer(),
}) => {
  // useEffect(() => {
  //   setTimeout(async () => {
  //     const html = renderToStaticMarkup(
  //       <Body letter={letter} layout={layout} />
  //     );
  //     const source = document.getElementById("letter-body");
  //     const letterContainer = document.getElementById("letter-preview");

  //     if (letterContainer) {
  //       letterContainer.style.fontFamily = layout.paper.fontFamily;
  //     }

  //     setLayoutProperties(layout);
  //     await previewer.preview(source, [cssUrl], letterContainer);
  //     source.remove();
  //   }, 500);
  // }, [letter, layout]);

  return (
    <>
      {/* <JssProvider> */}
      <div id="letter-body">
        <Body letter={letter} layout={layout} />
      </div>
      <div id="letter-preview"></div>
      {/* </JssProvider> */}
    </>
  );
};
